#!/usr/bin/env node

import { spawn } from 'child_process';

async function testWorkflow() {
  console.log('🧪 Testing Sarah\'s workflow tool...');
  
  const sarah = spawn('node', ['apps/sarah-business-analyst/dist/index.js'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    cwd: '/root/AISDLC'
  });

  let output = '';
  
  sarah.stdout.on('data', (data) => {
    const text = data.toString();
    output += text;
    console.log('📤 Sarah:', text.trim());
  });

  sarah.stderr.on('data', (data) => {
    console.log('🔍 Debug:', data.toString().trim());
  });

  // Wait for <PERSON> to start
  await new Promise(resolve => setTimeout(resolve, 3000));

  // Test: List tools
  console.log('\n📝 Requesting tool list...');
  const listRequest = {
    jsonrpc: "2.0",
    id: 1,
    method: "tools/list"
  };
  
  sarah.stdin.write(JSON.stringify(listRequest) + '\n');
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Test: Call workflow tool
  console.log('\n📝 Testing workflow tool...');
  const workflowRequest = {
    jsonrpc: "2.0",
    id: 2,
    method: "tools/call",
    params: {
      name: "complete-document-workflow",
      arguments: {
        issueNumber: 123,
        projectNumber: 97,
        documentUrl: "https://github.com/Infinisoft-inc/github-test/blob/main/test.md",
        documentTitle: "Test Document"
      }
    }
  };
  
  sarah.stdin.write(JSON.stringify(workflowRequest) + '\n');
  await new Promise(resolve => setTimeout(resolve, 3000));

  sarah.kill();
  console.log('\n✅ Test completed');
}

testWorkflow().catch(console.error);
