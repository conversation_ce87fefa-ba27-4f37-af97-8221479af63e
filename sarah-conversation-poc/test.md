# Sarah Conversation POC - Test Guide

## 🎯 Purpose
Test Sarah's template-driven conversation intelligence using minimal MCP server.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
cd sarah-conversation-poc
npm install
```

### 2. Build the Server
```bash
npm run build
```

### 3. Test with MCP Inspector
```bash
npx @modelcontextprotocol/inspector node dist/index.js
```

## 🧪 Test Scenarios

### Scenario 1: New Project
1. **Tool:** `get-project-template`
   - **projectName:** "CRM System Upgrade"
2. **Expected:** Sarah gets prompt to start gathering business case information

### Scenario 2: Update Template
1. **Tool:** `update-project-template`
   - **projectName:** "CRM System Upgrade"
   - **problemDefinition:** "Customer support response times are too slow, causing customer dissatisfaction"
2. **Expected:** Template updated, <PERSON> gets prompt to continue gathering info

### Scenario 3: Multiple Updates
Continue updating template with:
- **stakeholders:** "Customer service team, customers, sales department"
- **businessImpact:** "Losing $50,000 monthly in potential revenue from frustrated customers"
- **proposedSolution:** "New ticketing system with automated routing and AI responses"

### Scenario 4: Complete Template
Fill all fields and use `finalize-business-case` to generate final document.

## 🎭 Conversation Flow Test

### Expected Sarah Behavior:
1. **Welcome** - Introduces herself as AI Business Analyst
2. **Template Awareness** - Knows what information she needs
3. **Focused Questions** - Asks one question at a time
4. **Natural Flow** - Builds on previous answers
5. **Progress Tracking** - Shows completion percentage
6. **Completion Detection** - Knows when ready to finalize

### Test Prompts:
```
User: "Hi Sarah, I want to create a business case for my project"
→ Sarah should call get-project-template and ask about project name

User: "It's a CRM system upgrade to improve customer support"
→ Sarah should call get-project-template("CRM System Upgrade") and start asking questions

User: "We have slow response times causing customer complaints"
→ Sarah should call update-project-template with problemDefinition and ask follow-up
```

## ✅ Success Criteria

### Template Management:
- ✅ Creates new templates for new projects
- ✅ Loads existing templates for ongoing projects  
- ✅ Updates templates with new information
- ✅ Tracks completion percentage accurately

### Conversation Intelligence:
- ✅ Asks relevant questions based on empty fields
- ✅ Uses natural, conversational language
- ✅ Builds context from previous responses
- ✅ Knows when enough information is gathered

### Document Generation:
- ✅ Generates professional business case document
- ✅ Includes all gathered information
- ✅ Follows standard business case format

## 🐛 Debugging

### Check Memory State:
Use `list-projects` tool to see all projects in memory.

### Check Template State:
Use `get-project-template` to see current template completion.

### Reset Memory:
Restart the MCP server to clear in-memory storage.

## 🎯 Next Steps After POC

If this POC works well:
1. **Add File Persistence** - Replace in-memory with file storage
2. **Add More AI Teammates** - Alex (Architect), Jordan (Project Manager)
3. **Add GitHub Integration** - Post documents to repositories
4. **Add Status Updates** - Update GitHub issues automatically
5. **Add Multi-User Support** - Handle multiple users and projects

## 🚀 Expected Outcome

Sarah should conduct intelligent, natural conversations to gather business case information, demonstrating that template-driven conversation management works effectively with MCP tools.
