/**
 * Simple In-Memory Storage for <PERSON>'s Conversations
 * POC - No file system, just JavaScript objects
 */

export interface BusinessCaseTemplate {
  projectName: string;
  problemDefinition: string;
  stakeholders: string;
  businessImpact: string;
  proposedSolution: string;
  successCriteria: string;
  roi: string;
  timeline: string;
  risks: string;
  lastUpdated: string;
  completionPercentage: number;
}

// In-memory storage - simple JavaScript object
const projectMemory: Record<string, BusinessCaseTemplate> = {};

/**
 * Create a new empty business case template
 */
export function createEmptyTemplate(projectName: string): BusinessCaseTemplate {
  return {
    projectName,
    problemDefinition: '',
    stakeholders: '',
    businessImpact: '',
    proposedSolution: '',
    successCriteria: '',
    roi: '',
    timeline: '',
    risks: '',
    lastUpdated: new Date().toISOString(),
    completionPercentage: 0
  };
}

/**
 * Save project template to memory
 */
export function saveMemory(uniqueName: string, template: BusinessCaseTemplate): void {
  template.lastUpdated = new Date().toISOString();
  template.completionPercentage = calculateCompletionPercentage(template);
  projectMemory[uniqueName] = { ...template };
  
  console.log(`💾 Saved project: ${uniqueName} (${template.completionPercentage}% complete)`);
}

/**
 * Get project template from memory
 */
export function getMemory(uniqueName: string): BusinessCaseTemplate | null {
  const template = projectMemory[uniqueName];
  if (template) {
    console.log(`📂 Loaded project: ${uniqueName} (${template.completionPercentage}% complete)`);
    return { ...template };
  }
  
  console.log(`📂 Project not found: ${uniqueName}`);
  return null;
}

/**
 * List all projects in memory
 */
export function listProjects(): string[] {
  return Object.keys(projectMemory);
}

/**
 * Calculate completion percentage based on filled fields
 */
function calculateCompletionPercentage(template: BusinessCaseTemplate): number {
  const fields = [
    'problemDefinition',
    'stakeholders', 
    'businessImpact',
    'proposedSolution',
    'successCriteria',
    'roi',
    'timeline',
    'risks'
  ];
  
  const filledFields = fields.filter(field => {
    const value = template[field as keyof BusinessCaseTemplate];
    return typeof value === 'string' && value.trim().length > 0;
  });
  
  return Math.round((filledFields.length / fields.length) * 100);
}

/**
 * Get template requirements for prompting
 */
export function getTemplateRequirements(): string {
  return `
BUSINESS CASE TEMPLATE REQUIREMENTS:
====================================

1. PROBLEM DEFINITION: Clear description of the problem being solved
2. STAKEHOLDERS: Key people and groups affected by the project  
3. BUSINESS IMPACT: Cost/impact of NOT solving this problem
4. PROPOSED SOLUTION: High-level approach to solving the problem
5. SUCCESS CRITERIA: How success will be measured
6. ROI: Expected return on investment and benefits
7. TIMELINE: Expected project timeline and milestones
8. RISKS: Potential risks and mitigation strategies

Each field should be filled with specific, detailed information gathered through conversation.
`;
}

/**
 * Format template for display in prompts
 */
export function formatTemplateForPrompt(template: BusinessCaseTemplate): string {
  return `
CURRENT BUSINESS CASE TEMPLATE:
===============================
Project: ${template.projectName}
Completion: ${template.completionPercentage}%
Last Updated: ${template.lastUpdated}

1. PROBLEM DEFINITION: ${template.problemDefinition || '[EMPTY - NEEDS INFORMATION]'}

2. STAKEHOLDERS: ${template.stakeholders || '[EMPTY - NEEDS INFORMATION]'}

3. BUSINESS IMPACT: ${template.businessImpact || '[EMPTY - NEEDS INFORMATION]'}

4. PROPOSED SOLUTION: ${template.proposedSolution || '[EMPTY - NEEDS INFORMATION]'}

5. SUCCESS CRITERIA: ${template.successCriteria || '[EMPTY - NEEDS INFORMATION]'}

6. ROI: ${template.roi || '[EMPTY - NEEDS INFORMATION]'}

7. TIMELINE: ${template.timeline || '[EMPTY - NEEDS INFORMATION]'}

8. RISKS: ${template.risks || '[EMPTY - NEEDS INFORMATION]'}
`;
}
