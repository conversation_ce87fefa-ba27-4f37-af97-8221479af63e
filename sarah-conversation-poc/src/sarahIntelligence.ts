/**
 * <PERSON>'s Intelligence System - Strategic Thinking and Business Case Gathering
 * Adapted from <PERSON>'s proven conversation intelligence
 */

import { 
  saveMemory, 
  getMemory, 
  createEmptyTemplate, 
  type BusinessCaseTemplate 
} from './memory.js';

export class SarahIntelligence {
  private lastQuestionTime: Date | null = null;
  private awaitingResponse: boolean = false;
  private currentQuestionContext: string = '';
  private currentProjectName: string = '';

  /**
   * Process user input and generate intelligent response
   */
  processInput(userInput: string, projectName?: string): string {
    // If project name is provided, use it
    if (projectName) {
      this.currentProjectName = projectName;
    }

    // Check if this is a response to a pending question
    if (this.awaitingResponse) {
      return this.handleQuestionResponse(userInput);
    }

    // Analyze input and determine response type
    if (this.isOffTopic(userInput)) {
      return this.handleOffTopicResponse(userInput);
    }

    if (this.isProjectInitiation(userInput)) {
      return this.initiateProject(userInput);
    }

    if (this.isBusinessCaseDiscussion(userInput)) {
      return this.continueBusinessCaseDiscussion(userInput);
    }

    // Default intelligent response
    return this.generateContextualResponse(userInput);
  }

  /**
   * Check if user went off-topic from current question
   */
  private isOffTopic(userInput: string): boolean {
    if (!this.currentQuestionContext) return false;
    
    // Simple off-topic detection - if user mentions completely unrelated topics
    const offTopicKeywords = ['blue', 'weather', 'random', 'unrelated'];
    const lowerInput = userInput.toLowerCase();
    
    return offTopicKeywords.some(keyword => lowerInput.includes(keyword)) &&
           !this.isBusinessRelevant(userInput);
  }

  private isBusinessRelevant(input: string): boolean {
    const businessKeywords = ['business', 'project', 'solution', 'problem', 'stakeholder', 'benefit', 'cost', 'timeline', 'requirement'];
    const lowerInput = input.toLowerCase();
    return businessKeywords.some(keyword => lowerInput.includes(keyword));
  }

  /**
   * Handle off-topic responses with intelligent challenge
   */
  private handleOffTopicResponse(userInput: string): string {
    return `I notice you mentioned "${userInput}" but I was asking about ${this.currentQuestionContext}. Are you going off-scope here? Let's stay focused on gathering the business case information. 

${this.currentQuestionContext}`;
  }

  /**
   * Detect if user is starting a new project
   */
  private isProjectInitiation(userInput: string): boolean {
    const initiationKeywords = ['new project', 'working on', 'building', 'developing', 'creating', 'business case'];
    const lowerInput = userInput.toLowerCase();
    return initiationKeywords.some(keyword => lowerInput.includes(keyword));
  }

  /**
   * Start project discovery process
   */
  private initiateProject(userInput: string): string {
    // Extract project name if possible
    const projectName = this.extractProjectName(userInput);
    this.currentProjectName = projectName;
    
    // Create or get existing template
    const uniqueName = projectName.toLowerCase().replace(/\s+/g, '-') + '-business-case';
    let template = getMemory(uniqueName);
    
    if (!template) {
      template = createEmptyTemplate(projectName);
      saveMemory(uniqueName, template);
    }

    const response = `Excellent! I understand we're working on "${projectName}". As your AI Business Analyst, I need to gather comprehensive information to create a solid business case.

Let's start with the foundation: **What specific problem are we trying to solve with this project?** 

I need to understand the core issue that's driving this initiative.`;

    this.setAwaitingResponse("the specific problem we're trying to solve", response);
    return response;
  }

  private extractProjectName(input: string): string {
    // Simple project name extraction
    const patterns = [
      /working on (.+)/i,
      /building (.+)/i,
      /developing (.+)/i,
      /creating (.+)/i,
      /business case for (.+)/i
    ];

    for (const pattern of patterns) {
      const match = input.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return this.currentProjectName || 'AI-SDLC Project';
  }

  /**
   * Continue business case discussion
   */
  private continueBusinessCaseDiscussion(userInput: string): string {
    // Get current template
    const uniqueName = this.currentProjectName.toLowerCase().replace(/\s+/g, '-') + '-business-case';
    let template = getMemory(uniqueName);
    
    if (!template) {
      template = createEmptyTemplate(this.currentProjectName);
    }

    // Update template with user's response based on current context
    this.updateTemplateFromResponse(template, userInput);
    
    // Save updated template
    saveMemory(uniqueName, template);

    // Determine next question
    const nextTopic = this.getNextBusinessCaseTopic(template);
    if (nextTopic) {
      return this.askNextBusinessCaseQuestion(nextTopic);
    } else {
      return this.generateBusinessCaseSummary(template);
    }
  }

  private updateTemplateFromResponse(template: BusinessCaseTemplate, userInput: string): void {
    // Map current question context to template field
    const contextToField: Record<string, keyof BusinessCaseTemplate> = {
      'the specific problem we\'re trying to solve': 'problemDefinition',
      'stakeholders': 'stakeholders',
      'business impact': 'businessImpact',
      'proposed solution': 'proposedSolution',
      'success criteria': 'successCriteria',
      'roi': 'roi',
      'timeline': 'timeline',
      'risks': 'risks'
    };

    const field = contextToField[this.currentQuestionContext];
    if (field && typeof template[field] === 'string') {
      // Append to existing content or set if empty
      const currentValue = template[field] as string;
      (template as any)[field] = currentValue ? `${currentValue}\n\n${userInput}` : userInput;
    }
  }

  private isBusinessCaseDiscussion(userInput: string): boolean {
    return this.currentProjectName !== '' && this.awaitingResponse;
  }

  private getNextBusinessCaseTopic(template: BusinessCaseTemplate): string | null {
    const topics = [
      { key: 'stakeholders', name: 'stakeholders' },
      { key: 'businessImpact', name: 'business impact' },
      { key: 'proposedSolution', name: 'proposed solution' },
      { key: 'successCriteria', name: 'success criteria' },
      { key: 'roi', name: 'roi' },
      { key: 'timeline', name: 'timeline' },
      { key: 'risks', name: 'risks' }
    ];

    for (const topic of topics) {
      const value = template[topic.key as keyof BusinessCaseTemplate] as string;
      if (!value || value.trim() === '') {
        return topic.name;
      }
    }

    return null; // All topics completed
  }

  private askNextBusinessCaseQuestion(topic: string): string {
    const questions = {
      'stakeholders': 'Now, let\'s talk about **stakeholders**. Who are the key people and groups affected by this project? Who will be involved, who needs to approve, and who will use the solution?',
      'business impact': 'What **business impact** do you expect from this solution? How will it benefit the organization? Can you quantify the benefits or costs of NOT solving this problem?',
      'proposed solution': 'What is your **proposed solution**? How do you envision solving this problem? What approach or technology are you considering?',
      'success criteria': 'How will we measure success? What are the **specific criteria** that will tell us this project has achieved its goals?',
      'roi': 'What **return on investment** do you expect? Can you estimate the financial benefits, cost savings, or revenue increases this project will generate?',
      'timeline': 'What\'s your expected **timeline** for this project? When would you like to see this completed? Are there any important deadlines or milestones?',
      'risks': 'What **risks and challenges** do you foresee? What could go wrong with this project? What obstacles might we encounter and how would you mitigate them?'
    };

    const question = questions[topic as keyof typeof questions] || `Tell me about ${topic}.`;
    
    this.setAwaitingResponse(topic, question);
    
    return question;
  }

  /**
   * Handle responses to specific questions
   */
  private handleQuestionResponse(userInput: string): string {
    this.awaitingResponse = false;
    
    // Process the response and continue
    return this.continueBusinessCaseDiscussion(userInput);
  }

  /**
   * Set Sarah to await a specific response
   */
  private setAwaitingResponse(context: string, question: string): void {
    this.awaitingResponse = true;
    this.currentQuestionContext = context;
    this.lastQuestionTime = new Date();
  }

  /**
   * Generate contextual response based on conversation history
   */
  private generateContextualResponse(userInput: string): string {
    return `I understand. Let me think about this in the context of our business case development. Can you provide more specific details about how this relates to our project goals?`;
  }

  /**
   * Generate business case summary when complete
   */
  private generateBusinessCaseSummary(template: BusinessCaseTemplate): string {
    return `🎉 **Excellent! I have gathered all the information needed for the business case.**

Here's a summary of what we discussed:

**Project:** ${template.projectName}

**Problem Definition:** ${template.problemDefinition}

**Stakeholders:** ${template.stakeholders}

**Business Impact:** ${template.businessImpact}

**Proposed Solution:** ${template.proposedSolution}

**Success Criteria:** ${template.successCriteria}

**ROI:** ${template.roi}

**Timeline:** ${template.timeline}

**Risks:** ${template.risks}

I'll now create a comprehensive business case document with all this information. You can use the **finalize-business-case** tool to generate the final document.

Great work! This business case is ready for stakeholder review.`;
  }

  /**
   * Check for engagement timeouts and follow up
   */
  checkEngagement(): string | null {
    if (this.awaitingResponse && this.lastQuestionTime) {
      const timeSinceQuestion = Date.now() - this.lastQuestionTime.getTime();
      const oneMinute = 60 * 1000;
      
      if (timeSinceQuestion > oneMinute) {
        this.lastQuestionTime = new Date(); // Reset timer
        return `Hey, are you there? I asked about ${this.currentQuestionContext} and it seems important for our business case. Should we continue, or do you need a break?`;
      }
    }
    
    return null;
  }

  // Public getters for external access
  isAwaitingResponse(): boolean {
    return this.awaitingResponse;
  }

  getCurrentContext(): string {
    return this.currentQuestionContext;
  }

  getCurrentProject(): string {
    return this.currentProjectName;
  }
}
