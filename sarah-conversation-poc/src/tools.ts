/**
 * <PERSON>'s Conversation Tools
 * Simple tools for template-driven conversation management with intelligence
 */

import {
  saveMemory,
  getMemory,
  createEmptyTemplate,
  listProjects,
  formatTemplateForPrompt,
  getTemplateRequirements,
  type BusinessCaseTemplate
} from './memory.js';

import { SarahIntelligence } from './sarahIntelligence.js';

// Global Sarah intelligence instance
const sarahIntelligence = new SarahIntelligence();

/**
 * Tool: get-project-template
 * Load existing project or create new one
 */
export function getProjectTemplate(projectName: string): {
  template: BusinessCaseTemplate;
  isNew: boolean;
  prompt: string;
} {
  const uniqueName = projectName.toLowerCase().replace(/\s+/g, '-') + '-business-case';
  let template = getMemory(uniqueName);
  let isNew = false;
  
  if (!template) {
    template = createEmptyTemplate(projectName);
    saveMemory(uniqueName, template);
    isNew = true;
  }
  
  const prompt = createConversationPrompt(template, isNew);
  
  return { template, isNew, prompt };
}

/**
 * Tool: update-project-template  
 * Update template with new information
 */
export function updateProjectTemplate(
  projectName: string, 
  updates: Partial<BusinessCaseTemplate>
): {
  template: BusinessCaseTemplate;
  prompt: string;
} {
  const uniqueName = projectName.toLowerCase().replace(/\s+/g, '-') + '-business-case';
  let template = getMemory(uniqueName);
  
  if (!template) {
    template = createEmptyTemplate(projectName);
  }
  
  // Update template with new information
  template = { ...template, ...updates };
  saveMemory(uniqueName, template);
  
  const prompt = createConversationPrompt(template, false);
  
  return { template, prompt };
}

/**
 * Tool: list-projects
 * Show all projects in memory
 */
export function listAllProjects(): string[] {
  return listProjects();
}

/**
 * Tool: intelligent-conversation
 * Process user input through Sarah's intelligence system
 */
export function intelligentConversation(
  userMessage: string,
  projectName?: string
): {
  response: string;
  awaitingResponse: boolean;
  currentContext: string;
  currentProject: string;
} {
  const response = sarahIntelligence.processInput(userMessage, projectName);

  return {
    response,
    awaitingResponse: sarahIntelligence.isAwaitingResponse(),
    currentContext: sarahIntelligence.getCurrentContext(),
    currentProject: sarahIntelligence.getCurrentProject()
  };
}

/**
 * Create intelligent conversation prompt for Sarah
 */
function createConversationPrompt(template: BusinessCaseTemplate, isNew: boolean): string {
  const templateDisplay = formatTemplateForPrompt(template);
  const requirements = getTemplateRequirements();
  
  if (isNew) {
    return `You are Sarah, AI Business Analyst. A new business case project has been started.

${templateDisplay}

${requirements}

INSTRUCTIONS:
- Welcome the user and explain you'll help create a comprehensive business case
- Ask intelligent questions to gather information for EMPTY fields
- Be conversational and natural, not robotic
- Ask one focused question at a time
- Use update-project-template tool when you receive relevant information
- Focus on the most important missing information first

Start by asking about the problem definition if it's empty, or the most critical missing field.`;
  } else {
    return `You are Sarah, AI Business Analyst. Continue working on this business case project.

${templateDisplay}

${requirements}

INSTRUCTIONS:
- Review the current template state above
- Ask intelligent questions to gather information for EMPTY fields
- Be conversational and build on previous information
- Ask one focused question at a time  
- Use update-project-template tool when you receive relevant information
- If template is complete (no EMPTY fields), offer to finalize the business case

Continue the conversation by asking about the most important missing information.`;
  }
}

/**
 * Check if template is complete
 */
export function isTemplateComplete(template: BusinessCaseTemplate): boolean {
  return template.completionPercentage >= 100;
}

/**
 * Generate final business case document
 */
export function generateBusinessCaseDocument(template: BusinessCaseTemplate): string {
  return `# Business Case: ${template.projectName}

**Date:** ${new Date().toLocaleDateString()}
**Prepared by:** Sarah - AI Business Analyst
**Status:** Draft for Review

## Executive Summary

This business case outlines the justification for ${template.projectName}, addressing critical business needs and expected outcomes.

## Problem Definition

${template.problemDefinition}

## Stakeholders

${template.stakeholders}

## Business Impact

${template.businessImpact}

## Proposed Solution

${template.proposedSolution}

## Success Criteria

${template.successCriteria}

## Return on Investment

${template.roi}

## Timeline

${template.timeline}

## Risks and Mitigation

${template.risks}

## Recommendation

Based on the analysis above, we recommend proceeding with ${template.projectName} to address the identified business needs and achieve the expected benefits.

---

*This business case was created through collaborative discussion with Sarah, AI Business Analyst, following the AI-SDLC methodology.*`;
}
