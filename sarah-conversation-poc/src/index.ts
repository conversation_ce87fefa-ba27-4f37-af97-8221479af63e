#!/usr/bin/env node

/**
 * Sarah Conversation POC - Minimal MCP Server
 * Tests template-driven conversation intelligence
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

import {
  getProjectTemplate,
  updateProjectTemplate,
  listAllProjects,
  isTemplateComplete,
  generateBusinessCaseDocument
} from './tools.js';

/**
 * Create and configure the MCP server
 */
const server = new Server(
  {
    name: 'sarah-conversation-poc',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

/**
 * Tool: get-project-template
 * Load or create project template and get conversation prompt
 */
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: 'get-project-template',
        description: 'Load existing project template or create new one. Returns conversation prompt for <PERSON>.',
        inputSchema: {
          type: 'object',
          properties: {
            projectName: {
              type: 'string',
              description: 'Name of the project for the business case'
            }
          },
          required: ['projectName']
        }
      },
      {
        name: 'update-project-template',
        description: 'Update project template with new information gathered from conversation.',
        inputSchema: {
          type: 'object',
          properties: {
            projectName: {
              type: 'string',
              description: 'Name of the project'
            },
            problemDefinition: {
              type: 'string',
              description: 'Problem being solved'
            },
            stakeholders: {
              type: 'string', 
              description: 'Key stakeholders and their concerns'
            },
            businessImpact: {
              type: 'string',
              description: 'Impact of not solving the problem'
            },
            proposedSolution: {
              type: 'string',
              description: 'Proposed solution approach'
            },
            successCriteria: {
              type: 'string',
              description: 'How success will be measured'
            },
            roi: {
              type: 'string',
              description: 'Expected return on investment'
            },
            timeline: {
              type: 'string',
              description: 'Project timeline and milestones'
            },
            risks: {
              type: 'string',
              description: 'Risks and mitigation strategies'
            }
          },
          required: ['projectName']
        }
      },
      {
        name: 'list-projects',
        description: 'List all projects currently in memory.',
        inputSchema: {
          type: 'object',
          properties: {},
          required: []
        }
      },
      {
        name: 'finalize-business-case',
        description: 'Generate final business case document when template is complete.',
        inputSchema: {
          type: 'object',
          properties: {
            projectName: {
              type: 'string',
              description: 'Name of the project'
            }
          },
          required: ['projectName']
        }
      }
    ]
  };
});

/**
 * Handle tool execution
 */
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  try {
    switch (name) {
      case 'get-project-template': {
        const { projectName } = args as { projectName: string };
        const result = getProjectTemplate(projectName);
        
        return {
          content: [
            {
              type: 'text',
              text: `📋 **Project Template Loaded: ${projectName}**

${result.isNew ? '🆕 **New Project Created**' : '📂 **Existing Project Loaded**'}

**Completion:** ${result.template.completionPercentage}%

---

${result.prompt}

---

**Next Steps:**
- Ask focused questions to gather missing information
- Use update-project-template when you receive relevant details
- Be conversational and natural in your approach`
            }
          ]
        };
      }

      case 'update-project-template': {
        const { projectName, ...updates } = args as any;
        const result = updateProjectTemplate(projectName, updates);
        
        return {
          content: [
            {
              type: 'text',
              text: `✅ **Template Updated: ${projectName}**

**Completion:** ${result.template.completionPercentage}%

---

${result.prompt}

---

${isTemplateComplete(result.template) 
  ? '🎉 **Template Complete!** You can now use finalize-business-case to generate the final document.'
  : '📝 **Continue gathering information** for the remaining empty fields.'
}`
            }
          ]
        };
      }

      case 'list-projects': {
        const projects = listAllProjects();
        
        return {
          content: [
            {
              type: 'text',
              text: `📂 **Projects in Memory:**

${projects.length > 0 
  ? projects.map(p => `• ${p}`).join('\n')
  : 'No projects found. Use get-project-template to create a new project.'
}

**Total Projects:** ${projects.length}`
            }
          ]
        };
      }

      case 'finalize-business-case': {
        const { projectName } = args as { projectName: string };
        const result = getProjectTemplate(projectName);
        
        if (!isTemplateComplete(result.template)) {
          return {
            content: [
              {
                type: 'text',
                text: `❌ **Cannot Finalize Business Case**

**Project:** ${projectName}
**Completion:** ${result.template.completionPercentage}%

The business case template is not complete. Please gather more information for the empty fields before finalizing.`
              }
            ]
          };
        }
        
        const document = generateBusinessCaseDocument(result.template);
        
        return {
          content: [
            {
              type: 'text',
              text: `🎉 **Business Case Document Generated**

**Project:** ${projectName}
**Status:** Ready for Review

---

${document}

---

**Next Steps:**
1. Review the business case document above
2. Make any necessary revisions
3. Share with stakeholders for approval
4. Proceed to next AI-SDLC phase (Requirements Gathering)`
            }
          ]
        };
      }

      default:
        throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `❌ **Error executing ${name}:**

${error instanceof Error ? error.message : String(error)}

Please check your parameters and try again.`
        }
      ],
      isError: true
    };
  }
});

/**
 * Start the server
 */
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error('🤖 Sarah Conversation POC MCP Server started');
}

main().catch((error) => {
  console.error('❌ Server failed to start:', error);
  process.exit(1);
});
