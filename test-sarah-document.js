#!/usr/bin/env node

/**
 * Test script to verify <PERSON>'s GitHub integration
 */

import { spawn } from 'child_process';

const testDocument = `# GitHub Integration Test

This is a test document to verify that <PERSON> can actually save documents to GitHub.

**Test Details:**
- Date: ${new Date().toISOString()}
- Purpose: Verify real GitHub integration
- Expected: Document should appear in Infinisoft-inc/github-test repository

## Test Content

This document should be saved to:
- Repository: Infinisoft-inc/github-test
- Path: projects/test-project/docs/github-integration-test.md
- Branch: main

If you can see this document in GitHub, the integration is working! 🎉
`;

async function testSarah() {
  console.log('🧪 Testing Sarah\'s GitHub integration...');
  
  const sarah = spawn('node', ['apps/sarah-business-analyst/dist/index.js'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    cwd: '/root/AISDLC'
  });

  let output = '';
  
  sarah.stdout.on('data', (data) => {
    output += data.toString();
    console.log('📤 Sarah output:', data.toString());
  });

  sarah.stderr.on('data', (data) => {
    console.log('🔍 Sarah debug:', data.toString());
  });

  // Wait for Sarah to start
  await new Promise(resolve => setTimeout(resolve, 3000));

  // First set project context
  const setProjectRequest = {
    jsonrpc: "2.0",
    id: 1,
    method: "tools/call",
    params: {
      name: "set-project",
      arguments: {
        projectName: "GitHub Integration Test",
        githubRepo: "github-test"
      }
    }
  };

  console.log('📝 Setting project context...');
  sarah.stdin.write(JSON.stringify(setProjectRequest) + '\n');

  // Wait for response
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Then send save-document request
  const saveRequest = {
    jsonrpc: "2.0",
    id: 2,
    method: "tools/call",
    params: {
      name: "save-document",
      arguments: {
        projectName: "GitHub Integration Test",
        document: testDocument,
        fileName: "github-integration-test.md"
      }
    }
  };

  console.log('📝 Sending save-document request...');
  sarah.stdin.write(JSON.stringify(saveRequest) + '\n');

  // Wait for response
  await new Promise(resolve => setTimeout(resolve, 5000));

  sarah.kill();
  
  console.log('✅ Test completed. Check the output above for results.');
  console.log('🔗 Check GitHub: https://github.com/Infinisoft-inc/github-test');
}

testSarah().catch(console.error);
