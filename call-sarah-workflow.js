#!/usr/bin/env node

import { spawn } from 'child_process';

async function callSarahWorkflow() {
  console.log('🎯 Calling Sarah\'s workflow for A1 Group business case...');
  
  const sarah = spawn('node', ['apps/sarah-business-analyst/dist/index.js'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    cwd: '/root/AISDLC'
  });

  sarah.stdout.on('data', (data) => {
    console.log('📤 Sarah:', data.toString().trim());
  });

  sarah.stderr.on('data', (data) => {
    console.log('🔍 Debug:', data.toString().trim());
  });

  // Wait for <PERSON> to start
  await new Promise(resolve => setTimeout(resolve, 3000));

  // Call the workflow tool with A1 Group details
  const workflowRequest = {
    jsonrpc: "2.0",
    id: 1,
    method: "tools/call",
    params: {
      name: "complete-document-workflow",
      arguments: {
        issueNumber: 123,
        projectNumber: 97,
        documentUrl: "https://github.com/Infinisoft-inc/github-test/blob/main/projects/a1-group-social-services-automation/docs/A1-Group-Social-Services-Business-Case.md",
        documentTitle: "A1 Group Social Services Business Case",
        reviewerMention: "@martin",
        taskDescription: "Business case document completed - $4.5M annual savings with zero investment"
      }
    }
  };
  
  console.log('📝 Executing workflow for A1 Group business case...');
  sarah.stdin.write(JSON.stringify(workflowRequest) + '\n');
  
  // Wait for response
  await new Promise(resolve => setTimeout(resolve, 5000));

  sarah.kill();
  console.log('\n✅ Workflow execution completed');
}

callSarahWorkflow().catch(console.error);
