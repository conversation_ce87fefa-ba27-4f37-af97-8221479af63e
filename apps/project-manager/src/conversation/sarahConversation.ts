/**
 * <PERSON>'s Intelligent Conversation System
 * Focus: Natural conversation intelligence for business case gathering
 */

export interface ConversationState {
  phase: 'starting' | 'gathering' | 'clarifying' | 'ready' | 'completed';
  gatheredInfo: Record<string, any>;
  currentTopic: string | null;
  conversationHistory: Array<{
    speaker: 'sarah' | 'user';
    message: string;
    timestamp: Date;
  }>;
  completionScore: number; // 0-100, how complete is the information
}

// Hardcoded business case requirements (focusing on intelligence, not parsing)
export const BUSINESS_CASE_REQUIREMENTS = {
  problemDefinition: {
    key: 'problemDefinition',
    description: 'Clear definition of the problem being solved',
    questions: [
      'What specific problem are you trying to solve?',
      'Can you describe the current situation that needs improvement?',
      'What challenges are you facing that this project would address?'
    ],
    followUps: [
      'Can you give me a specific example of this problem?',
      'How often does this problem occur?',
      'Who is most affected by this problem?'
    ]
  },
  stakeholders: {
    key: 'stakeholders',
    description: 'Key people and groups affected by or involved in the project',
    questions: [
      'Who are the main stakeholders for this project?',
      'Which teams or departments would be involved?',
      'Who would be the primary users of this solution?'
    ],
    followUps: [
      'What are their main concerns or requirements?',
      'How do they currently handle this problem?',
      'Who would need to approve this project?'
    ]
  },
  businessImpact: {
    key: 'businessImpact',
    description: 'The cost or impact of not solving this problem',
    questions: [
      'What happens if this problem isn\'t solved?',
      'How does this problem affect the business currently?',
      'What are the costs of the current situation?'
    ],
    followUps: [
      'Can you quantify the impact in terms of time, money, or resources?',
      'Are there any compliance or risk issues?',
      'How does this affect customer satisfaction or employee productivity?'
    ]
  },
  proposedSolution: {
    key: 'proposedSolution',
    description: 'High-level approach to solving the problem',
    questions: [
      'What solution do you have in mind?',
      'How do you envision solving this problem?',
      'What approach would you like to take?'
    ],
    followUps: [
      'What are the key features or capabilities needed?',
      'Are there any existing solutions you\'ve considered?',
      'What would make this solution successful?'
    ]
  },
  successCriteria: {
    key: 'successCriteria',
    description: 'How success will be measured',
    questions: [
      'How will you know if this project is successful?',
      'What metrics would you use to measure success?',
      'What would the ideal outcome look like?'
    ],
    followUps: [
      'What specific numbers or targets are you aiming for?',
      'How would you measure user satisfaction or adoption?',
      'What timeline would you consider successful?'
    ]
  },
  roi: {
    key: 'roi',
    description: 'Expected return on investment and benefits',
    questions: [
      'What benefits do you expect from this project?',
      'What\'s the expected return on investment?',
      'How would this project save time or money?'
    ],
    followUps: [
      'Can you estimate the financial impact?',
      'What cost savings or revenue increases do you expect?',
      'How long would it take to see these benefits?'
    ]
  },
  timeline: {
    key: 'timeline',
    description: 'Expected project timeline and milestones',
    questions: [
      'What\'s your expected timeline for this project?',
      'When would you like to see this completed?',
      'Are there any important deadlines or milestones?'
    ],
    followUps: [
      'What factors might affect the timeline?',
      'Are there any dependencies on other projects?',
      'What would be the minimum viable timeline?'
    ]
  },
  risks: {
    key: 'risks',
    description: 'Potential risks and mitigation strategies',
    questions: [
      'What are the main risks or challenges you foresee?',
      'What could go wrong with this project?',
      'What obstacles might we encounter?'
    ],
    followUps: [
      'How would you mitigate these risks?',
      'What contingency plans would you consider?',
      'What resources would help reduce these risks?'
    ]
  }
};

/**
 * Sarah's Conversation Intelligence
 */
export class SarahConversation {
  private state: ConversationState;

  constructor() {
    this.state = {
      phase: 'starting',
      gatheredInfo: {},
      currentTopic: null,
      conversationHistory: [],
      completionScore: 0
    };
  }

  /**
   * Sarah's intelligent response to user input
   */
  async processUserInput(userMessage: string): Promise<string> {
    // Add user message to history
    this.addToHistory('user', userMessage);

    // Process the message based on current phase
    let sarahResponse: string;

    switch (this.state.phase) {
      case 'starting':
        sarahResponse = this.handleStarting(userMessage);
        break;
      case 'gathering':
        sarahResponse = this.handleGathering(userMessage);
        break;
      case 'clarifying':
        sarahResponse = this.handleClarifying(userMessage);
        break;
      case 'ready':
        sarahResponse = this.handleReady(userMessage);
        break;
      default:
        sarahResponse = "I'm not sure how to respond right now. Can you help me understand what you need?";
    }

    // Add Sarah's response to history
    this.addToHistory('sarah', sarahResponse);

    return sarahResponse;
  }

  private handleStarting(userMessage: string): string {
    // Sarah introduces herself and starts the business case conversation
    this.state.phase = 'gathering';
    this.state.currentTopic = 'problemDefinition';

    return `Hi! I'm Sarah, your AI Business Analyst. I'm here to help you create a comprehensive business case for your project.

I'll ask you some questions to understand your project better. We can have a natural conversation - feel free to share as much or as little as you'd like with each answer.

Let's start with the fundamentals: **What specific problem are you trying to solve with this project?**`;
  }

  private handleGathering(userMessage: string): string {
    // Extract information from user's response
    this.extractInformation(userMessage);
    
    // Update completion score
    this.updateCompletionScore();

    // Decide next action: ask follow-up, move to next topic, or clarify
    if (this.needsClarification(userMessage)) {
      this.state.phase = 'clarifying';
      return this.askClarification();
    } else if (this.shouldMoveToNextTopic()) {
      return this.moveToNextTopic();
    } else {
      return this.askFollowUp();
    }
  }

  private handleClarifying(userMessage: string): string {
    // Process clarification and return to gathering
    this.extractInformation(userMessage);
    this.state.phase = 'gathering';
    
    if (this.shouldMoveToNextTopic()) {
      return this.moveToNextTopic();
    } else {
      return this.askFollowUp();
    }
  }

  private handleReady(userMessage: string): string {
    return `Perfect! I have all the information I need to create a comprehensive business case. 

**Summary of what we discussed:**
${this.generateSummary()}

I'll now create the business case document and post it to your GitHub repository. You'll be able to review and refine it as needed.

Would you like me to proceed with creating the document?`;
  }

  private extractInformation(userMessage: string): void {
    // Simple information extraction (in real implementation, this would be more sophisticated)
    if (this.state.currentTopic) {
      if (!this.state.gatheredInfo[this.state.currentTopic]) {
        this.state.gatheredInfo[this.state.currentTopic] = [];
      }
      this.state.gatheredInfo[this.state.currentTopic].push(userMessage);
    }
  }

  private needsClarification(userMessage: string): boolean {
    // Simple heuristics for when clarification is needed
    const shortResponse = userMessage.length < 20;
    const vague = userMessage.toLowerCase().includes('not sure') || 
                  userMessage.toLowerCase().includes('maybe') ||
                  userMessage.toLowerCase().includes('i think');
    
    return shortResponse || vague;
  }

  private shouldMoveToNextTopic(): boolean {
    // Check if current topic has enough information
    const currentInfo = this.state.gatheredInfo[this.state.currentTopic || ''];
    return currentInfo && currentInfo.length >= 2; // At least 2 exchanges on current topic
  }

  private moveToNextTopic(): string {
    const topics = Object.keys(BUSINESS_CASE_REQUIREMENTS);
    const currentIndex = topics.indexOf(this.state.currentTopic || '');
    
    if (currentIndex < topics.length - 1) {
      const nextTopic = topics[currentIndex + 1];
      this.state.currentTopic = nextTopic;
      const requirement = BUSINESS_CASE_REQUIREMENTS[nextTopic as keyof typeof BUSINESS_CASE_REQUIREMENTS];
      
      return `Great! Now let's talk about ${requirement.description.toLowerCase()}.

${requirement.questions[0]}`;
    } else {
      // All topics covered
      this.state.phase = 'ready';
      return this.handleReady('');
    }
  }

  private askFollowUp(): string {
    if (!this.state.currentTopic) return "Can you tell me more about that?";
    
    const requirement = BUSINESS_CASE_REQUIREMENTS[this.state.currentTopic as keyof typeof BUSINESS_CASE_REQUIREMENTS];
    const followUps = requirement.followUps;
    
    // Pick a relevant follow-up question
    return followUps[Math.floor(Math.random() * followUps.length)];
  }

  private askClarification(): string {
    return "Can you tell me a bit more about that? I'd like to understand the details better.";
  }

  private updateCompletionScore(): void {
    const totalTopics = Object.keys(BUSINESS_CASE_REQUIREMENTS).length;
    const coveredTopics = Object.keys(this.state.gatheredInfo).length;
    this.state.completionScore = Math.round((coveredTopics / totalTopics) * 100);
  }

  private generateSummary(): string {
    let summary = '';
    for (const [topic, info] of Object.entries(this.state.gatheredInfo)) {
      const requirement = BUSINESS_CASE_REQUIREMENTS[topic as keyof typeof BUSINESS_CASE_REQUIREMENTS];
      summary += `\n**${requirement.description}:** ${Array.isArray(info) ? info.join(' ') : info}`;
    }
    return summary;
  }

  private addToHistory(speaker: 'sarah' | 'user', message: string): void {
    this.state.conversationHistory.push({
      speaker,
      message,
      timestamp: new Date()
    });
  }

  // Public methods for external access
  getState(): ConversationState {
    return { ...this.state };
  }

  getCompletionScore(): number {
    return this.state.completionScore;
  }

  isReady(): boolean {
    return this.state.phase === 'ready';
  }
}
