import { describe, it, expect, beforeEach } from 'vitest';
import { SarahConversation } from '../sarahConversation.js';

describe('Sarah Conversation Intelligence', () => {
  let sarah: SarahConversation;

  beforeEach(() => {
    sarah = new SarahConversation();
  });

  describe('Starting Phase', () => {
    it('should introduce herself and ask about the problem', async () => {
      const response = await sarah.processUserInput("Hi <PERSON>, I want to create a business case");
      
      expect(response).toContain("Hi! I'm <PERSON>");
      expect(response).toContain("Business Analyst");
      expect(response).toContain("What specific problem");
      expect(sarah.getState().phase).toBe('gathering');
      expect(sarah.getState().currentTopic).toBe('problemDefinition');
    });
  });

  describe('Information Gathering', () => {
    it('should gather problem definition and ask follow-up', async () => {
      // Start conversation
      await sarah.processUserInput("Hi <PERSON>, let's work on a business case");
      
      // Provide problem definition
      const response = await sarah.processUserInput("We need to improve our customer support system because response times are too slow");
      
      const state = sarah.getState();
      expect(state.gatheredInfo.problemDefinition).toBeDefined();
      expect(state.gatheredInfo.problemDefinition[0]).toContain("customer support system");
      expect(response).toContain("?"); // Should ask a follow-up question
    });

    it('should ask for clarification on vague responses', async () => {
      await sarah.processUserInput("Hi Sarah, let's work on a business case");
      
      const response = await sarah.processUserInput("Not sure, maybe something with computers");
      
      expect(sarah.getState().phase).toBe('clarifying');
      expect(response).toContain("tell me a bit more");
    });

    it('should move to next topic after sufficient information', async () => {
      await sarah.processUserInput("Hi Sarah, let's work on a business case");
      
      // Provide detailed problem definition
      await sarah.processUserInput("We need to improve our customer support system because response times are too slow and customers are complaining");
      
      // Provide follow-up information
      const response = await sarah.processUserInput("This happens daily and affects about 500 customers per day, costing us potential revenue");
      
      // Should move to stakeholders topic
      expect(response).toContain("stakeholder");
      expect(sarah.getState().currentTopic).toBe('stakeholders');
    });
  });

  describe('Completion Detection', () => {
    it('should track completion score as topics are covered', async () => {
      await sarah.processUserInput("Hi Sarah, let's work on a business case");
      
      expect(sarah.getCompletionScore()).toBe(0);
      
      // Cover problem definition
      await sarah.processUserInput("We need to improve our customer support system");
      await sarah.processUserInput("This affects 500 customers daily");
      
      expect(sarah.getCompletionScore()).toBeGreaterThan(0);
      expect(sarah.getCompletionScore()).toBeLessThan(100);
    });

    it('should be ready when all topics are covered', async () => {
      // This would be a longer test simulating a full conversation
      expect(sarah.isReady()).toBe(false);
      
      // After covering all topics, should be ready
      // (In a real test, we'd simulate the full conversation)
    });
  });

  describe('Conversation Memory', () => {
    it('should maintain conversation history', async () => {
      await sarah.processUserInput("Hi Sarah");
      await sarah.processUserInput("We have a problem with slow response times");
      
      const history = sarah.getState().conversationHistory;
      expect(history).toHaveLength(4); // 2 user messages + 2 sarah responses
      expect(history[0].speaker).toBe('user');
      expect(history[1].speaker).toBe('sarah');
      expect(history[0].message).toBe('Hi Sarah');
    });

    it('should generate summary of gathered information', async () => {
      await sarah.processUserInput("Hi Sarah, let's work on a business case");
      await sarah.processUserInput("We need to improve customer support response times");
      await sarah.processUserInput("This affects our customer satisfaction scores");
      
      const state = sarah.getState();
      expect(state.gatheredInfo.problemDefinition).toBeDefined();
      expect(state.gatheredInfo.problemDefinition.length).toBe(2);
    });
  });

  describe('Natural Conversation Flow', () => {
    it('should handle conversational responses naturally', async () => {
      await sarah.processUserInput("Hi Sarah, let's work on a business case");
      
      const response1 = await sarah.processUserInput("Well, we're having issues with our customer support. People are waiting too long for responses.");
      expect(response1).toContain("?"); // Should ask follow-up
      
      const response2 = await sarah.processUserInput("Yeah, sometimes customers wait 2-3 hours for a response, and they get frustrated and leave negative reviews.");
      expect(response2).toContain("?"); // Should continue gathering info
    });

    it('should adapt questions based on previous answers', async () => {
      await sarah.processUserInput("Hi Sarah, let's work on a business case");
      await sarah.processUserInput("We need a new CRM system");
      
      // Sarah should ask relevant follow-up questions about CRM
      const response = await sarah.processUserInput("Our current system is outdated and slow");
      expect(response.toLowerCase()).toMatch(/(specific|example|often|affected)/);
    });
  });
});
