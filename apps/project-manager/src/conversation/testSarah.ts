#!/usr/bin/env node

/**
 * Interactive test for <PERSON>'s conversation intelligence
 * Run this to have a real conversation with <PERSON>
 */

import { SarahConversation } from './sarahConversation.js';
import * as readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function testSarahConversation() {
  console.log('🤖 Sarah Conversation Intelligence Test');
  console.log('=====================================');
  console.log('Type "quit" to exit, "status" to see completion score\n');

  const sarah = new SarahConversation();

  // Start the conversation
  console.log('👤 User: Hi <PERSON>, I want to create a business case for my project');
  const initialResponse = await sarah.processUserInput('Hi <PERSON>, I want to create a business case for my project');
  console.log(`🤖 Sarah: ${initialResponse}\n`);

  // Interactive conversation loop
  const askQuestion = () => {
    rl.question('👤 User: ', async (userInput) => {
      if (userInput.toLowerCase() === 'quit') {
        console.log('\n🤖 Sarah: Thanks for the conversation! Goodbye!');
        rl.close();
        return;
      }

      if (userInput.toLowerCase() === 'status') {
        const state = sarah.getState();
        console.log(`\n📊 Conversation Status:`);
        console.log(`   Phase: ${state.phase}`);
        console.log(`   Current Topic: ${state.currentTopic}`);
        console.log(`   Completion Score: ${sarah.getCompletionScore()}%`);
        console.log(`   Ready to Create Document: ${sarah.isReady()}`);
        console.log(`   Topics Covered: ${Object.keys(state.gatheredInfo).join(', ')}\n`);
        askQuestion();
        return;
      }

      try {
        const response = await sarah.processUserInput(userInput);
        console.log(`🤖 Sarah: ${response}\n`);

        if (sarah.isReady()) {
          console.log('🎉 Sarah is ready to create the business case document!');
          console.log('📊 Final completion score:', sarah.getCompletionScore() + '%');
          
          const state = sarah.getState();
          console.log('\n📋 Summary of gathered information:');
          for (const [topic, info] of Object.entries(state.gatheredInfo)) {
            console.log(`   ${topic}: ${Array.isArray(info) ? info.join(' | ') : info}`);
          }
          
          rl.close();
          return;
        }

        askQuestion();
      } catch (error) {
        console.error('❌ Error:', error);
        askQuestion();
      }
    });
  };

  askQuestion();
}

// Handle graceful shutdown
rl.on('close', () => {
  console.log('\nConversation ended. Goodbye!');
  process.exit(0);
});

// Start the test
testSarahConversation().catch(console.error);
