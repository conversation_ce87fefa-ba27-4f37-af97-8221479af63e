/**
 * Generate Document Tool - Single Responsibility
 * Only handles document generation
 */

import { TemplateManager } from '../services/templates/template-manager.js';
import { ProjectMemory } from '../project-memory.js';
import { promptProfessionalDocumentTemplate } from '../services/templates/prompt-professionnal-document-template.js';

export class GenerateDocumentTool {
  constructor(
    private templateManager: TemplateManager,
    private projectMemory: ProjectMemory
  ) {}

  async execute(args: { templateName: string; projectName?: string }) {
    const { templateName } = args;
    
    try {
      // Check if we have project information
      if (!this.projectMemory.hasInfo()) {
        throw new Error('No project information gathered yet. Please use the remember tool first.');
      }

      // Generate document prompt
      const result = this.templateManager.generatePrompt(templateName, this.projectMemory.getAll(), promptProfessionalDocumentTemplate);
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to generate prompt');
      }

      return {
        content: [{
          type: "text",
          text: result.prompt!
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: "text",
          text: `❌ **Failed to Generate Document**\n\nError: ${error instanceof Error ? error.message : String(error)}\n\nPlease ensure you have provided project information using the remember tool.`
        }]
      };
    }
  }

  getSchema() {
    return {
      name: "generate-document",
      description: "Generate LLM prompt for document creation",
      inputSchema: {
        type: "object",
        properties: {
          templateName: {
            type: "string",
            description: "Document template name",
            enum: ["business-case", "requirements", "architecture"]
          },
          projectName: { type: "string", description: "Name of the project (optional)" }
        },
        required: ["templateName"]
      }
    };
  }
}
