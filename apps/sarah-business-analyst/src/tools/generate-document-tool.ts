/**
 * Generate Document Tool - Single Responsibility
 * Only handles document generation
 */

import { DocumentTool } from '../document-tool.js';
import { ProjectMemory } from '../project-memory.js';

export class GenerateDocumentTool {
  constructor(
    private documentTool: DocumentTool,
    private projectMemory: ProjectMemory
  ) {}

  async execute(args: { templateName: string; projectName?: string }) {
    const { templateName } = args;
    
    try {
      // Check if we have project information
      if (!this.projectMemory.hasInfo()) {
        throw new Error('No project information gathered yet. Please use the remember tool first.');
      }

      // Generate document prompt
      const result = this.documentTool.generatePrompt(templateName, this.projectMemory.getAll());
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to generate prompt');
      }

      return {
        content: [{
          type: "text",
          text: result.prompt!
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: "text",
          text: `❌ **Failed to Generate Document**\n\nError: ${error instanceof Error ? error.message : String(error)}\n\nPlease ensure you have provided project information using the remember tool.`
        }]
      };
    }
  }

  getSchema() {
    return {
      name: "generate-document",
      description: "Generate LLM prompt for document creation",
      inputSchema: {
        type: "object",
        properties: {
          templateName: {
            type: "string",
            description: "Document template name",
            enum: ["business-case", "requirements", "architecture"]
          },
          projectName: { type: "string", description: "Name of the project (optional)" }
        },
        required: ["templateName"]
      }
    };
  }
}
