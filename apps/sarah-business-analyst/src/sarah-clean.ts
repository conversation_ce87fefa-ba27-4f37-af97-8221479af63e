#!/usr/bin/env node

/**
 * Clean Sarah Implementation - Single Responsibility
 * Only handles conversation and delegates to specialized tools
 */

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { CallToolRequestSchema, ListToolsRequestSchema } from "@modelcontextprotocol/sdk/types.js";

import { DocumentTool } from './document-tool.js';
import { ProjectMemory } from './project-memory.js';
import { StorageService, MockStorage } from './storage.js';

/**
 * Clean Sarah - Focused only on conversation logic
 */
export class Sarah {
  private server: Server;
  private documentTool: DocumentTool;
  private projectMemory: ProjectMemory;

  constructor(storageService?: StorageService) {
    this.server = new Server({ name: "sarah-clean", version: "1.0.0" }, { capabilities: { tools: {} } });
    this.documentTool = new DocumentTool(storageService || new MockStorage());
    this.projectMemory = new ProjectMemory();
    this.setupTools();
  }

  private setupTools() {
    // List tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: "set-project",
          description: "Set project name",
          inputSchema: {
            type: "object",
            properties: { name: { type: "string", description: "Project name" } },
            required: ["name"],
          },
        },
        {
          name: "remember",
          description: "Save project information",
          inputSchema: {
            type: "object",
            properties: { info: { type: "string", description: "Information to remember" } },
            required: ["info"],
          },
        },
        {
          name: "generate-document",
          description: "Generate document from template",
          inputSchema: {
            type: "object",
            properties: {
              template: { type: "string", enum: ["business-case", "requirements", "architecture"] },
            },
            required: ["template"],
          },
        },
        {
          name: "save-document",
          description: "Save document to storage",
          inputSchema: {
            type: "object",
            properties: {
              path: { type: "string", description: "File path" },
              content: { type: "string", description: "Document content" },
            },
            required: ["path", "content"],
          },
        },
        {
          name: "status",
          description: "Get current status",
          inputSchema: { type: "object", properties: {}, required: [] },
        },
      ],
    }));

    // Handle tools
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      switch (name) {
        case "set-project":
          const { name: projectName } = args as { name: string };
          this.projectMemory.setName(projectName);
          return { content: [{ type: "text", text: `✅ Project set: ${projectName}` }] };

        case "remember":
          const { info } = args as { info: string };
          this.projectMemory.add(info);
          return { content: [{ type: "text", text: `✅ Remembered: ${info}` }] };

        case "generate-document":
          const { template } = args as { template: string };
          const result = this.documentTool.generatePrompt(template, this.projectMemory.getAll());
          if (!result.success) {
            return { content: [{ type: "text", text: `❌ ${result.error}` }] };
          }
          return { content: [{ type: "text", text: result.prompt! }] };

        case "save-document":
          const { path, content } = args as { path: string; content: string };
          const saveResult = await this.documentTool.saveDocument(path, content);
          if (!saveResult.success) {
            return { content: [{ type: "text", text: `❌ Save failed: ${saveResult.error}` }] };
          }
          return { content: [{ type: "text", text: `✅ Saved to: ${saveResult.url}` }] };

        case "status":
          const templates = this.documentTool.getTemplates().join(', ');
          const memory = this.projectMemory.getSummary();
          return {
            content: [{
              type: "text",
              text: `📊 Sarah Status\n${memory}\nTemplates: ${templates}`
            }]
          };

        default:
          throw new Error(`Unknown tool: ${name}`);
      }
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error("Clean Sarah running on stdio");
  }
}

// Run clean Sarah
const sarah = new Sarah();
sarah.run().catch(console.error);
