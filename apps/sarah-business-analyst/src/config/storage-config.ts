/**
 * Storage Configuration Examples
 * Shows how to configure different storage implementations
 */

import { StorageService, MockStorage, FileStorage, GitHubStorage } from '../services/storage/index.js';

export class StorageConfig {
  /**
   * Create storage based on environment or configuration
   */
  static createStorage(type: 'mock' | 'file' | 'github', options?: any): StorageService {
    switch (type) {
      case 'mock':
        return new MockStorage();
        
      case 'file':
        return new FileStorage(options?.basePath || './output');
        
      case 'github':
        if (!options?.token || !options?.owner || !options?.repo) {
          throw new Error('GitHub storage requires token, owner, and repo');
        }
        return new GitHubStorage(
          options.token,
          options.owner,
          options.repo,
          options.branch || 'main'
        );
        
      default:
        throw new Error(`Unknown storage type: ${type}`);
    }
  }

  /**
   * Create GitHub storage from environment variables
   */
  static createGitHubFromEnv(): GitHubStorage {
    const token = process.env.GITHUB_TOKEN;
    const owner = process.env.GITHUB_OWNER || 'Infinisoft-inc';
    const repo = process.env.GITHUB_REPO;
    const branch = process.env.GITHUB_BRANCH || 'main';

    if (!token || !repo) {
      throw new Error('GITHUB_TOKEN and GITHUB_REPO environment variables are required');
    }

    return new GitHubStorage(token, owner, repo, branch);
  }
}
