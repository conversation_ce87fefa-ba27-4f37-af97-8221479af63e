/**
 * Storage Configuration with Strongly Typed Options
 * Uses discriminative unions for type safety
 */

import { StorageService, MockStorage, FileStorage, GitHubStorage } from '../services/storage/index.js';

/**
 * Strongly typed storage options using discriminative unions
 */
export type StorageOptions =
  | { type: 'mock' }
  | { type: 'file'; basePath: string }
  | {
    type: 'github';
    token: string;
    owner: string;
    repo: string;
    branch?: string
  };

/**
 * Doppler configuration structure (single secret pattern)
 */
export interface DopplerConfig {
  storage?: {
    type: 'mock' | 'file' | 'github';
    basePath?: string;
  };
  github?: {
    token: string;
    owner?: string;
    repo: string;
    branch?: string;
  };
}

export class StorageConfig {
  /**
   * Create storage with strongly typed options
   */
  static createStorage(options: StorageOptions): StorageService {
    switch (options.type) {
      case 'mock':
        return new MockStorage();

      case 'file':
        return new FileStorage(options.basePath);

      case 'github':
        return new GitHubStorage(
          options.token,
          options.owner,
          options.repo,
          options.branch || 'main'
        );

      default:
        // TypeScript ensures this is never reached
        const _exhaustive: never = options;
        throw new Error(`Unknown storage type: ${JSON.stringify(_exhaustive)}`);
    }
  }

  /**
   * Create storage from Doppler configuration (single secret pattern)
   */
  static createFromDoppler(): StorageService {
    const dopplerConfig = this.parseDopplerConfig();

    const storageType = dopplerConfig.storage?.type || 'mock';

    switch (storageType) {
      case 'mock':
        return this.createStorage({ type: 'mock' });

      case 'file':
        const basePath = dopplerConfig.storage?.basePath || './output';
        return this.createStorage({ type: 'file', basePath });

      case 'github':
        const github = dopplerConfig.github;
        if (!github?.token || !github?.repo) {
          throw new Error('Doppler config missing required github.token or github.repo');
        }

        return this.createStorage({
          type: 'github',
          token: github.token,
          owner: github.owner || 'Infinisoft-inc',
          repo: github.repo,
          branch: github.branch || 'main'
        });

      default:
        throw new Error(`Unknown storage type in Doppler config: ${storageType}`);
    }
  }

  /**
   * Parse Doppler configuration from single environment variable
   */
  private static parseDopplerConfig(): DopplerConfig {
    const configString = process.env.DOPPLER_CONFIG;

    if (!configString) {
      // Default configuration for development
      return {
        storage: { type: 'mock' }
      };
    }

    try {
      return JSON.parse(configString);
    } catch (error) {
      throw new Error(`Invalid DOPPLER_CONFIG JSON: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
