#!/usr/bin/env node

/**
 * Clean Sarah - Single Responsibility Architecture
 * Only handles MCP server setup and coordination
 */

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { CallToolRequestSchema, ListToolsRequestSchema } from "@modelcontextprotocol/sdk/types.js";

import { ToolRegistry } from './tool-registry.js';
import { ProjectMemory } from './project-memory.js';
import { DocumentTool } from './document-tool.js';
import { MockStorage } from './storage.js';
import { SARAH_TEMPLATES } from './templates.js';

import { RememberTool } from './tools/remember-tool.js';
import { SetProjectTool, ProjectContext } from './tools/set-project-tool.js';
import { GenerateDocumentTool } from './tools/generate-document-tool.js';
import { SaveDocumentTool } from './tools/save-document-tool.js';
import { ConversationTool } from './tools/conversation-tool.js';

export class Sarah {
  private server: Server;
  private toolRegistry: ToolRegistry;
  private projectContext?: ProjectContext;

  constructor() {
    this.server = new Server(
      { name: "sarah", version: "1.0.0" },
      { capabilities: { tools: {} } }
    );

    this.toolRegistry = new ToolRegistry();
    this.setupTools();
    this.setupHandlers();
  }

  private setupTools() {
    // Create shared dependencies
    const projectMemory = new ProjectMemory();
    const storage = new MockStorage();
    const documentTool = new DocumentTool(storage, SARAH_TEMPLATES);

    // Register tools
    this.toolRegistry.register(new RememberTool(projectMemory));
    this.toolRegistry.register(new SetProjectTool(projectMemory));
    this.toolRegistry.register(new GenerateDocumentTool(documentTool, projectMemory));
    this.toolRegistry.register(new SaveDocumentTool(documentTool, () => this.projectContext));
    this.toolRegistry.register(new ConversationTool("Sarah", "AI Business Analyst"));
  }

  private setupHandlers() {
    // List tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: this.toolRegistry.getSchemas()
    }));

    // Call tools
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      // Handle set-project specially to capture context
      if (name === "set-project") {
        const result = await this.toolRegistry.execute(name, args);
        if (result.context) {
          this.projectContext = result.context;
        }
        return result;
      }
      
      return await this.toolRegistry.execute(name, args);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error("Clean Sarah running on stdio");
  }
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  const sarah = new Sarah();
  sarah.run().catch(console.error);
}
