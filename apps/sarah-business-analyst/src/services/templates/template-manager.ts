/**
 * Template Manager - Single Responsibility
 * Only handles template management and prompt generation
 */

export type PromptTemplate = (template: Template, gatheredInfo: string[]) => string;

export interface Template {
  name: string;
  sections: string[];
  prompt: string;
}

export interface PromptResult {
  success: boolean;
  prompt?: string;
  error?: string;
}

export class TemplateManager {
  private templates: Map<string, Template> = new Map();

  constructor(templates?: Template[]) {
    if (templates) {
      templates.forEach(template => this.addTemplate(template.name, template));
    }
  }

  addTemplate(name: string, template: Template): void {
    this.templates.set(name, template);
  }

  getTemplates(): string[] {
    return Array.from(this.templates.keys());
  }

  generatePrompt(templateName: string, gatheredInfo: string[], promptTemplate: PromptTemplate): PromptResult {
    if (gatheredInfo.length === 0) {
      return { success: false, error: 'No information provided' };
    }

    const template = this.templates.get(templateName);
    if (!template) {
      return { success: false, error: `Template '${templateName}' not found` };
    }

    const prompt = this.buildPrompt(template, gatheredInfo, promptTemplate);
    return { success: true, prompt };
  }

  private buildPrompt(template: Template, gatheredInfo: string[], promptTemplate: PromptTemplate): string {
    return promptTemplate(template, gatheredInfo);
  }
}
