import { Prompt } from "@modelcontextprotocol/sdk/types";
import { PromptTemplate, Template } from "./template-manager";

export const promptProfessionalDocumentTemplate: PromptTemplate = (template: Template, gatheredInfo: string[]): string => {
    const infoList = gatheredInfo.map((info, index) => `${index + 1}. ${info}`).join('\n');
    const sectionsList = template.sections.map((section, index) => `${index + 1}. ${section}`).join('\n');

    return `Create a professional ${template.name} document.

GATHERED INFORMATION:
${infoList}

TEMPLATE SECTIONS:
${sectionsList}

${template.prompt}

Use markdown formatting with proper headers and structure.`;
}