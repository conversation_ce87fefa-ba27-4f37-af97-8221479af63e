/**
 * Prompt Registry - MCP Prompts for Centralized Prompt Management
 * Centralizes all prompts for reusability, consistency, and versioning
 */

export interface PromptArgument {
  name: string;
  description: string;
  required?: boolean;
}

export interface Prompt {
  name: string;
  description: string;
  arguments?: PromptArgument[];
}

export interface PromptMessage {
  role: "user" | "assistant" | "system";
  content: {
    type: "text";
    text: string;
  };
}

export interface PromptResponse {
  description: string;
  messages: PromptMessage[];
}

export class PromptRegistry {
  private prompts = new Map<string, (args: any) => PromptResponse>();

  constructor() {
    this.registerDefaultPrompts();
  }

  /**
   * Register default Sarah prompts
   */
  private registerDefaultPrompts(): void {
    // Business case generation prompt
    this.register("business-case-generation", (args: { projectInfo?: string[]; templateType?: string }) => {
      const projectInfo = args.projectInfo || [];
      const projectInfoText = projectInfo.length > 0
        ? projectInfo.map((info, i) => `${i + 1}. ${info}`).join('\n')
        : 'No specific project information provided.';

      return {
        description: "Generate a comprehensive business case document",
        messages: [
          {
            role: "user",
            content: {
              type: "text",
              text: `Create a professional business case document using the following information:

PROJECT INFORMATION:
${projectInfoText}

TEMPLATE SECTIONS:
1. Problem Definition - What specific problem are we solving?
2. Stakeholders - Who are the key people affected?
3. Business Impact - What's the cost of not solving this?
4. Proposed Solution - What's the high-level approach?
5. Success Criteria - How will we measure success?
6. ROI - What's the expected return on investment?
7. Timeline - What's the expected timeline?
8. Risks - What are the potential risks?

Please create a comprehensive business case that justifies the project investment. Use markdown formatting with proper headers and structure.`
            }
          }
        ]
      };
    });

    // Conversation context prompt
    this.register("conversation-context", (args: { message: string; context?: string; teammateName?: string; teammateRole?: string }) => {
      const { message, context = "general", teammateName = "Sarah", teammateRole = "AI Business Analyst" } = args;
      
      const basePrompt = `You are ${teammateName}, ${teammateRole}. Respond to the user's message using your personality and knowledge.

USER MESSAGE: "${message}"

Respond as ${teammateName} would, using your specific personality and expertise. Focus on your role and capabilities.

IMPORTANT: 
1. Call the remember tool with any important information you want to remember from this conversation
2. Call the speech_response tool with the format "${teammateName}: [your response]" and from ai_teammate ${teammateName} to send it to the voice application with proper voice identification for unique TTS voices.`;

      const projectContext = context === "project" ? `

PROJECT CONTEXT INSTRUCTIONS:
You are working on gathering business case information. Use the business case template below to guide your conversation:

BUSINESS CASE TEMPLATE:
1. Problem Definition - What specific problem are we solving?
2. Stakeholders - Who are the key people affected?
3. Business Impact - What's the cost of not solving this?
4. Proposed Solution - What's the high-level approach?
5. Success Criteria - How will we measure success?
6. ROI - What's the expected return on investment?
7. Timeline - What's the expected timeline?
8. Risks - What are the potential risks?

GATHERING INSTRUCTIONS:
- Ask focused questions to gather missing information for the template
- Be conversational and natural, not robotic
- Build on previous responses and ask follow-up questions
- Use the remember tool to save important details as you gather them
- When you have comprehensive information for all template sections, offer to create the document
- If user accepts, call the generate-document tool with templateName "business-case" to generate the document` : '';

      return {
        description: "Generate conversation prompt with context",
        messages: [
          {
            role: "user",
            content: {
              type: "text",
              text: basePrompt + projectContext
            }
          }
        ]
      };
    });

    // Document refinement prompt
    this.register("document-refinement", (args: { currentDocument?: string; newRequirements?: string[]; changeType?: string }) => {
      const currentDocument = args.currentDocument || 'No document provided.';
      const newRequirements = args.newRequirements || [];
      const requirementsText = newRequirements.length > 0
        ? newRequirements.map((req, i) => `${i + 1}. ${req}`).join('\n')
        : 'No new requirements specified.';

      return {
        description: "Refine existing document with new requirements",
        messages: [
          {
            role: "user",
            content: {
              type: "text",
              text: `Please refine the following document by incorporating new requirements:

CURRENT DOCUMENT:
${currentDocument}

NEW REQUIREMENTS:
${requirementsText}

REFINEMENT TYPE: ${args.changeType || 'Update and enhance'}

Please update the document to include the new requirements while maintaining consistency with the existing content. Highlight the changes made.`
            }
          }
        ]
      };
    });

    // Requirements gathering prompt
    this.register("requirements-gathering", (args: { projectType?: string; stakeholders?: string[]; constraints?: string[] }) => {
      const projectType = args.projectType || 'software';
      const stakeholdersText = args.stakeholders && args.stakeholders.length > 0
        ? `STAKEHOLDERS: ${args.stakeholders.join(', ')}`
        : '';
      const constraintsText = args.constraints && args.constraints.length > 0
        ? `CONSTRAINTS: ${args.constraints.join(', ')}`
        : '';

      return {
        description: "Guide requirements gathering conversation",
        messages: [
          {
            role: "user",
            content: {
              type: "text",
              text: `You are helping gather requirements for a ${projectType} project.

${stakeholdersText}
${constraintsText}

Guide the conversation to gather:
1. Functional Requirements - What the system must do
2. Non-Functional Requirements - Performance, security, usability
3. User Stories - From user perspective
4. Acceptance Criteria - How to verify requirements

Ask focused questions to uncover detailed requirements. Be thorough but conversational.`
            }
          }
        ]
      };
    });
  }

  /**
   * Register a new prompt
   */
  register(name: string, promptGenerator: (args: any) => PromptResponse): void {
    this.prompts.set(name, promptGenerator);
  }

  /**
   * Get list of available prompts
   */
  listPrompts(): Prompt[] {
    return [
      {
        name: "business-case-generation",
        description: "Generate comprehensive business case documents",
        arguments: [
          { name: "projectInfo", description: "Array of project information strings", required: true },
          { name: "templateType", description: "Type of template to use", required: false }
        ]
      },
      {
        name: "conversation-context",
        description: "Generate conversation prompts with context",
        arguments: [
          { name: "message", description: "User message to respond to", required: true },
          { name: "context", description: "Conversation context (general|project)", required: false },
          { name: "teammateName", description: "Name of the AI teammate", required: false },
          { name: "teammateRole", description: "Role of the AI teammate", required: false }
        ]
      },
      {
        name: "document-refinement",
        description: "Refine existing documents with new requirements",
        arguments: [
          { name: "currentDocument", description: "Current document content", required: true },
          { name: "newRequirements", description: "Array of new requirements", required: true },
          { name: "changeType", description: "Type of changes to make", required: false }
        ]
      },
      {
        name: "requirements-gathering",
        description: "Guide requirements gathering conversations",
        arguments: [
          { name: "projectType", description: "Type of project", required: true },
          { name: "stakeholders", description: "Array of stakeholder names", required: false },
          { name: "constraints", description: "Array of project constraints", required: false }
        ]
      }
    ];
  }

  /**
   * Get prompt by name with arguments
   */
  getPrompt(name: string, args: any = {}): PromptResponse {
    const promptGenerator = this.prompts.get(name);
    if (!promptGenerator) {
      throw new Error(`Prompt '${name}' not found`);
    }
    return promptGenerator(args);
  }
}
