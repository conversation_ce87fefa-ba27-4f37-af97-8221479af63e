import { StorageService } from '../storage.js';

/**
 * File system storage implementation
 */

export class FileStorage implements StorageService {
  constructor(private basePath: string = './output') { }

  async save(path: string, content: string) {
    try {
      const fs = await import('fs/promises');
      const pathModule = await import('path');
      const fullPath = pathModule.join(this.basePath, path);
      await fs.mkdir(pathModule.dirname(fullPath), { recursive: true });
      await fs.writeFile(fullPath, content, 'utf-8');
      return { success: true, url: `file://${fullPath}` };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  }
}
