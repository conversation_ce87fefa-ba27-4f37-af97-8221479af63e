import { StorageService } from "../storage.js";

/**
 * Mock storage for testing
 */

export class MockStorage implements StorageService {
  private docs = new Map<string, string>();

  async save(path: string, content: string) {
    this.docs.set(path, content);
    return { success: true, url: `mock://${path}` };
  }

  get(path: string) { return this.docs.get(path); }
  getAll() { return new Map(this.docs); }
}
