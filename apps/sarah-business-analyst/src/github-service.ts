/**
 * Storage Service Interface - Clean Architecture
 * Defines contract for document storage without implementation details
 */

export interface StorageService {
  saveDocument(path: string, content: string, metadata?: Record<string, any>): Promise<{
    success: boolean;
    url?: string;
    error?: string;
  }>;
}

/**
 * Simple file system storage implementation
 */
export class FileSystemStorage implements StorageService {
  private basePath: string;

  constructor(basePath: string = './output') {
    this.basePath = basePath;
  }

  async saveDocument(path: string, content: string, metadata?: Record<string, any>): Promise<{
    success: boolean;
    url?: string;
    error?: string;
  }> {
    try {
      const fs = await import('fs/promises');
      const pathModule = await import('path');

      const fullPath = pathModule.join(this.basePath, path);
      const dir = pathModule.dirname(fullPath);

      // Ensure directory exists
      await fs.mkdir(dir, { recursive: true });

      // Write file
      await fs.writeFile(fullPath, content, 'utf-8');

      return {
        success: true,
        url: `file://${fullPath}`,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }
}

/**
 * Mock storage for testing/demo purposes
 */
export class MockStorage implements StorageService {
  private savedDocuments: Map<string, string> = new Map();

  async saveDocument(path: string, content: string, metadata?: Record<string, any>): Promise<{
    success: boolean;
    url?: string;
    error?: string;
  }> {
    try {
      this.savedDocuments.set(path, content);

      return {
        success: true,
        url: `mock://storage/${path}`,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  getSavedDocument(path: string): string | undefined {
    return this.savedDocuments.get(path);
  }

  getAllSavedDocuments(): Map<string, string> {
    return new Map(this.savedDocuments);
  }
}
