/**
 * Reusable Document Tool - Single Responsibility
 * Can be extracted as a separate package
 */

import { StorageService } from './storage.js';

export interface Template {
  name: string;
  sections: string[];
  prompt: string;
}

export interface DocumentResult {
  success: boolean;
  prompt?: string;
  error?: string;
}

export interface SaveResult {
  success: boolean;
  url?: string;
  error?: string;
}

/**
 * Clean, reusable document generation tool
 */
export class DocumentTool {
  private templates: Map<string, Template> = new Map();

  constructor(private storage: StorageService, templates?: Template[]) {
    if (templates) {
      templates.forEach(template => this.addTemplate(template.name, template));
    }
  }

  /**
   * Generate LLM prompt for document creation
   */
  generatePrompt(templateName: string, gatheredInfo: string[]): DocumentResult {
    const template = this.templates.get(templateName);
    if (!template) {
      return { success: false, error: `Template '${templateName}' not found` };
    }

    if (gatheredInfo.length === 0) {
      return { success: false, error: 'No information provided' };
    }

    const sections = template.sections.map((s, i) => `${i + 1}. ${s}`).join('\n');
    const info = gatheredInfo.map((i, idx) => `${idx + 1}. ${i}`).join('\n');

    const prompt = `Create a professional ${template.name} document.

GATHERED INFORMATION:
${info}

TEMPLATE SECTIONS:
${sections}

${template.prompt}

Use markdown formatting with proper headers and structure.`;

    return { success: true, prompt };
  }

  /**
   * Save document using storage service
   */
  async saveDocument(path: string, content: string): Promise<SaveResult> {
    return await this.storage.save(path, content);
  }

  /**
   * Add custom template
   */
  addTemplate(name: string, template: Template): void {
    this.templates.set(name, template);
  }

  /**
   * Get available templates
   */
  getTemplates(): string[] {
    return Array.from(this.templates.keys());
  }


}
