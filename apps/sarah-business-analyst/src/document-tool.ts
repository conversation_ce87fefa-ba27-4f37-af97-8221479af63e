/**
 * Reusable Document Tool - Single Responsibility
 * Can be extracted as a separate package
 */

import { StorageService } from './storage.js';

export interface Template {
  name: string;
  sections: string[];
  prompt: string;
}

export interface DocumentResult {
  success: boolean;
  prompt?: string;
  error?: string;
}

export interface SaveResult {
  success: boolean;
  url?: string;
  error?: string;
}

/**
 * Clean, reusable document generation tool
 */
export class DocumentTool {
  private templates: Map<string, Template> = new Map();

  constructor(private storage: StorageService) {
    this.initializeDefaultTemplates();
  }

  /**
   * Generate LLM prompt for document creation
   */
  generatePrompt(templateName: string, gatheredInfo: string[]): DocumentResult {
    const template = this.templates.get(templateName);
    if (!template) {
      return { success: false, error: `Template '${templateName}' not found` };
    }

    if (gatheredInfo.length === 0) {
      return { success: false, error: 'No information provided' };
    }

    const sections = template.sections.map((s, i) => `${i + 1}. ${s}`).join('\n');
    const info = gatheredInfo.map((i, idx) => `${idx + 1}. ${i}`).join('\n');

    const prompt = `Create a professional ${template.name} document.

GATHERED INFORMATION:
${info}

TEMPLATE SECTIONS:
${sections}

${template.prompt}

Use markdown formatting with proper headers and structure.`;

    return { success: true, prompt };
  }

  /**
   * Save document using storage service
   */
  async saveDocument(path: string, content: string): Promise<SaveResult> {
    return await this.storage.save(path, content);
  }

  /**
   * Add custom template
   */
  addTemplate(name: string, template: Template): void {
    this.templates.set(name, template);
  }

  /**
   * Get available templates
   */
  getTemplates(): string[] {
    return Array.from(this.templates.keys());
  }

  /**
   * Initialize default templates
   */
  private initializeDefaultTemplates(): void {
    this.templates.set('business-case', {
      name: 'Business Case',
      sections: [
        'Problem Definition - What specific problem are we solving?',
        'Stakeholders - Who are the key people affected?',
        'Business Impact - What\'s the cost of not solving this?',
        'Proposed Solution - What\'s the high-level approach?',
        'Success Criteria - How will we measure success?',
        'ROI - What\'s the expected return on investment?',
        'Timeline - What\'s the expected timeline?',
        'Risks - What are the potential risks?'
      ],
      prompt: 'Create a comprehensive business case that justifies the project investment.'
    });

    this.templates.set('requirements', {
      name: 'Requirements Document',
      sections: [
        'Functional Requirements - What the system must do',
        'Non-Functional Requirements - Performance, security, usability',
        'User Stories - From user perspective',
        'Acceptance Criteria - How to verify requirements'
      ],
      prompt: 'Create a detailed requirements document that clearly defines what needs to be built.'
    });

    this.templates.set('architecture', {
      name: 'Architecture Document',
      sections: [
        'System Overview - High-level architecture',
        'Components - Major system components',
        'Data Flow - How data moves through the system',
        'Technology Stack - Technologies and frameworks'
      ],
      prompt: 'Create a comprehensive architecture document that guides technical implementation.'
    });
  }
}
