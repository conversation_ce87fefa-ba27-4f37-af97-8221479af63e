/**
 * Generic Document Generation System
 * Reusable tool for any AI teammate to generate and save documents
 * Follows Single Responsibility Principle (SRP)
 */

export interface DocumentTemplate {
  name: string;
  description: string;
  sections: string[];
  promptTemplate: string;
}

export interface ProjectContext {
  name: string;
  githubRepo: string;
  organization: string;
  docsPath: string;
}

export interface DocumentConfig {
  templates: Record<string, DocumentTemplate>;
  projectContext?: ProjectContext;
}

export class DocumentGenerator {
  private config: DocumentConfig;

  constructor(config: DocumentConfig) {
    this.config = config;
  }

  /**
   * Generate LLM prompt for document creation
   */
  generateDocumentPrompt(
    templateName: string, 
    gatheredInfo: string[], 
    projectName?: string
  ): string {
    const template = this.config.templates[templateName];
    if (!template) {
      throw new Error(`Template '${templateName}' not found`);
    }

    const sectionsText = template.sections.map((section, index) => 
      `${index + 1}. ${section}`
    ).join('\n');

    const gatheredInfoText = gatheredInfo.map((info, index) => 
      `${index + 1}. ${info}`
    ).join('\n');

    return `You are an expert analyst. Create a comprehensive ${template.description} using the following information:

GATHERED INFORMATION:
${gatheredInfoText}

DOCUMENT TEMPLATE SECTIONS:
${sectionsText}

${template.promptTemplate}

INSTRUCTIONS:
- Create a professional document with proper markdown formatting
- Use the template sections above to organize the information
- Fill each section with relevant details from the gathered information
- Add executive summary and recommendations
- Ensure the document is ready for stakeholder review
- Include proper headers, bullet points, and professional formatting

After generating the document, call the save-document tool with the generated content.`;
  }

  /**
   * Get available document templates
   */
  getAvailableTemplates(): string[] {
    return Object.keys(this.config.templates);
  }

  /**
   * Get template details
   */
  getTemplate(templateName: string): DocumentTemplate | undefined {
    return this.config.templates[templateName];
  }

  /**
   * Set project context
   */
  setProjectContext(context: ProjectContext): void {
    this.config.projectContext = context;
  }

  /**
   * Get project context
   */
  getProjectContext(): ProjectContext | undefined {
    return this.config.projectContext;
  }

  /**
   * Generate file path for document
   */
  generateFilePath(templateName: string, fileName?: string): string {
    const context = this.config.projectContext;
    if (!context) {
      throw new Error('Project context not set');
    }

    const defaultFileName = `${templateName}.md`;
    const finalFileName = fileName || defaultFileName;
    
    return `${context.docsPath}/${finalFileName}`;
  }

  /**
   * Get GitHub repository information
   */
  getGitHubInfo(): { repo: string; organization: string } | undefined {
    const context = this.config.projectContext;
    if (!context) return undefined;

    return {
      repo: context.githubRepo,
      organization: context.organization
    };
  }
}

// Default document templates
export const DEFAULT_TEMPLATES: Record<string, DocumentTemplate> = {
  'business-case': {
    name: 'Business Case',
    description: 'business case document',
    sections: [
      'Problem Definition - What specific problem are we solving?',
      'Stakeholders - Who are the key people affected?',
      'Business Impact - What\'s the cost of not solving this?',
      'Proposed Solution - What\'s the high-level approach?',
      'Success Criteria - How will we measure success?',
      'ROI - What\'s the expected return on investment?',
      'Timeline - What\'s the expected timeline?',
      'Risks - What are the potential risks?'
    ],
    promptTemplate: 'Create a comprehensive business case that justifies the project investment and outlines expected benefits.'
  },
  'requirements': {
    name: 'Requirements Document',
    description: 'requirements specification document',
    sections: [
      'Functional Requirements - What the system must do',
      'Non-Functional Requirements - Performance, security, usability',
      'User Stories - From user perspective',
      'Acceptance Criteria - How to verify requirements',
      'Dependencies - External systems and constraints',
      'Assumptions - What we\'re assuming to be true'
    ],
    promptTemplate: 'Create a detailed requirements document that clearly defines what needs to be built.'
  },
  'architecture': {
    name: 'Architecture Document',
    description: 'system architecture document',
    sections: [
      'System Overview - High-level architecture',
      'Components - Major system components',
      'Data Flow - How data moves through the system',
      'Technology Stack - Technologies and frameworks',
      'Security Considerations - Security measures',
      'Scalability - How the system will scale',
      'Integration Points - External system connections'
    ],
    promptTemplate: 'Create a comprehensive architecture document that guides technical implementation.'
  }
};
