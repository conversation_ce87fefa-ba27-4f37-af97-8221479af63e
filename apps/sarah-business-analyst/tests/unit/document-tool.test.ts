/**
 * Unit Tests for Template Manager
 */

import { promptProfessionalDocumentTemplate } from '../../src/services/templates/prompt-professionnal-document-template.js';
import { TemplateManager, Template } from '../../src/services/templates/template-manager.js';

describe('TemplateManager', () => {
  let templateManager: TemplateManager;

  const testTemplates: Template[] = [
    {
      name: 'test-template',
      sections: ['Section 1', 'Section 2'],
      prompt: 'Create a test document'
    }
  ];

  beforeEach(() => {
    templateManager = new TemplateManager(testTemplates);
  });

  test('should initialize with provided templates', () => {
    expect(templateManager.getTemplates()).toContain('test-template');
  });

  test('should initialize without templates', () => {
    const emptyManager = new TemplateManager();
    expect(emptyManager.getTemplates()).toEqual([]);
  });

  test('should add custom templates', () => {
    const customTemplate: Template = {
      name: 'custom',
      sections: ['Custom Section'],
      prompt: 'Custom prompt'
    };

    templateManager.addTemplate('custom', customTemplate);
    expect(templateManager.getTemplates()).toContain('custom');
  });

  test('should generate prompt successfully', () => {
    const gatheredInfo = ['Info 1', 'Info 2'];

    const result = templateManager.generatePrompt('test-template', gatheredInfo, promptProfessionalDocumentTemplate);

    expect(result.success).toBe(true);
    expect(result.prompt).toContain('Create a test document');
    expect(result.prompt).toContain('Info 1');
    expect(result.prompt).toContain('Info 2');
    expect(result.prompt).toContain('Section 1');
    expect(result.prompt).toContain('Section 2');
  });

  test('should fail with unknown template', () => {
    const result = templateManager.generatePrompt('unknown', ['info'], promptProfessionalDocumentTemplate);

    expect(result.success).toBe(false);
    expect(result.error).toContain('Template \'unknown\' not found');
  });

  test('should fail with no information', () => {
    const result = templateManager.generatePrompt('test-template', [], promptProfessionalDocumentTemplate);

    expect(result.success).toBe(false);
    expect(result.error).toBe('No information provided');
  });

});
