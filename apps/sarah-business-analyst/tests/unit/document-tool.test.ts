/**
 * Unit Tests for Document Tool
 */

import { DocumentTool, Template } from '../../src/document-tool.js';
import { MockStorage } from '../../src/storage.js';

describe('DocumentTool', () => {
  let storage: MockStorage;
  let documentTool: DocumentTool;
  
  const testTemplates: Template[] = [
    {
      name: 'test-template',
      sections: ['Section 1', 'Section 2'],
      prompt: 'Create a test document'
    }
  ];

  beforeEach(() => {
    storage = new MockStorage();
    documentTool = new DocumentTool(storage, testTemplates);
  });

  test('should initialize with provided templates', () => {
    expect(documentTool.getTemplates()).toContain('test-template');
  });

  test('should initialize without templates', () => {
    const emptyTool = new DocumentTool(storage);
    expect(emptyTool.getTemplates()).toEqual([]);
  });

  test('should add custom templates', () => {
    const customTemplate: Template = {
      name: 'custom',
      sections: ['Custom Section'],
      prompt: 'Custom prompt'
    };

    documentTool.addTemplate('custom', customTemplate);
    expect(documentTool.getTemplates()).toContain('custom');
  });

  test('should generate prompt successfully', () => {
    const gatheredInfo = ['Info 1', 'Info 2'];
    
    const result = documentTool.generatePrompt('test-template', gatheredInfo);
    
    expect(result.success).toBe(true);
    expect(result.prompt).toContain('Create a test document');
    expect(result.prompt).toContain('Info 1');
    expect(result.prompt).toContain('Info 2');
    expect(result.prompt).toContain('Section 1');
    expect(result.prompt).toContain('Section 2');
  });

  test('should fail with unknown template', () => {
    const result = documentTool.generatePrompt('unknown', ['info']);
    
    expect(result.success).toBe(false);
    expect(result.error).toContain('Template \'unknown\' not found');
  });

  test('should fail with no information', () => {
    const result = documentTool.generatePrompt('test-template', []);
    
    expect(result.success).toBe(false);
    expect(result.error).toBe('No information provided');
  });

  test('should save document successfully', async () => {
    const path = 'test/doc.md';
    const content = '# Test Document';
    
    const result = await documentTool.saveDocument(path, content);
    
    expect(result.success).toBe(true);
    expect(result.url).toBe(`mock://${path}`);
    expect(storage.get(path)).toBe(content);
  });

  test('should handle save errors', async () => {
    // Mock storage that fails
    const failingStorage = {
      save: jest.fn().mockResolvedValue({ success: false, error: 'Save failed' })
    };
    
    const tool = new DocumentTool(failingStorage as any);
    const result = await tool.saveDocument('test.md', 'content');
    
    expect(result.success).toBe(false);
    expect(result.error).toBe('Save failed');
  });
});
