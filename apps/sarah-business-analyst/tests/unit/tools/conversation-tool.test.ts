/**
 * Unit Tests for Conversation Tool
 */

import { ConversationTool } from '../../../src/tools/conversation-tool.js';

describe('ConversationTool', () => {
  let conversationTool: ConversationTool;

  beforeEach(() => {
    conversationTool = new ConversationTool('<PERSON>', 'AI Business Analyst');
  });

  test('should have correct schema', () => {
    const schema = conversationTool.getSchema();
    
    expect(schema.name).toBe('process-message-for-sarah');
    expect(schema.description).toContain('Process a message for <PERSON>');
    expect(schema.inputSchema.required).toContain('message');
  });

  test('should execute with general context', async () => {
    const args = { message: 'Hello Sarah', context: 'general' };
    
    const result = await conversationTool.execute(args);
    
    expect(result.content[0].type).toBe('text');
    expect(result.content[0].text).toContain('You are <PERSON>, AI Business Analyst');
    expect(result.content[0].text).toContain('Hello Sarah');
    expect(result.content[0].text).toContain('remember tool');
    expect(result.content[0].text).toContain('speech_response tool');
  });

  test('should execute with project context', async () => {
    const args = { message: 'Let\'s work on a project', context: 'project' };
    
    const result = await conversationTool.execute(args);
    
    expect(result.content[0].text).toContain('PROJECT CONTEXT INSTRUCTIONS');
    expect(result.content[0].text).toContain('BUSINESS CASE TEMPLATE');
    expect(result.content[0].text).toContain('Problem Definition');
    expect(result.content[0].text).toContain('generate-document tool');
  });

  test('should default to general context', async () => {
    const args = { message: 'Test message' };
    
    const result = await conversationTool.execute(args);
    
    expect(result.content[0].text).toContain('You are Sarah');
    expect(result.content[0].text).not.toContain('PROJECT CONTEXT');
  });

  test('should include teammate name in prompt', async () => {
    const customTool = new ConversationTool('Alex', 'Project Manager');
    const args = { message: 'Hello' };
    
    const result = await customTool.execute(args);
    
    expect(result.content[0].text).toContain('You are Alex, Project Manager');
    expect(result.content[0].text).toContain('Respond as Alex would');
  });

  test('should handle empty message', async () => {
    const args = { message: '' };
    
    const result = await conversationTool.execute(args);
    
    expect(result.content[0].type).toBe('text');
    expect(result.content[0].text).toContain('USER MESSAGE: ""');
  });
});
