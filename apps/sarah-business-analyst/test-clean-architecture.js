#!/usr/bin/env node

/**
 * Test script demonstrating the clean architecture with dependency injection
 */

import { FileStorage, MockStorage } from './dist/storage.js';
import { DocumentTool } from './dist/document-tool.js';
import { ProjectMemory } from './dist/project-memory.js';

async function testCleanArchitecture() {
  console.log('🧪 Testing Clean Architecture with SRP\n');

  // Test 1: Project Memory (Single Responsibility)
  console.log('🧠 Test 1: Project Memory');
  const memory = new ProjectMemory();
  memory.setName('AI Platform');
  memory.add('Development cycles are too slow');
  memory.add('80% time waste on non-essential tasks');
  memory.add('Voice-first interface needed');

  console.log('Memory Status:', memory.getSummary());
  console.log('Information Count:', memory.getAll().length);
  console.log('');

  // Test 2: Document Tool (Single Responsibility)
  console.log('📄 Test 2: Document Tool');
  const mockStorage = new MockStorage();
  const docTool = new DocumentTool(mockStorage);

  const promptResult = docTool.generatePrompt('business-case', memory.getAll());
  console.log('Prompt Generation:', promptResult.success ? 'Success' : 'Failed');
  console.log('Available Templates:', docTool.getTemplates().join(', '));
  console.log('');

  // Test 3: Storage Abstraction (Strategy Pattern)
  console.log('💾 Test 3: Storage Abstraction');
  const fileStorage = new FileStorage('./test-output');

  const mockResult = await mockStorage.save('test/mock.md', '# Mock Document');
  const fileResult = await fileStorage.save('test/file.md', '# File Document');

  console.log('Mock Storage:', mockResult.success ? 'Success' : 'Failed');
  console.log('File Storage:', fileResult.success ? 'Success' : 'Failed');
  console.log('');

  // Test 4: Clean Component Composition
  console.log('🔧 Test 4: Component Composition');
  const docToolWithFile = new DocumentTool(fileStorage);
  const saveResult = await docToolWithFile.saveDocument(
    'projects/ai-platform/business-case.md',
    '# AI Platform Business Case\n\nGenerated by clean architecture.'
  );
  console.log('Document Tool + File Storage:', saveResult.success ? 'Success' : 'Failed');
  console.log('');

  console.log('🎉 Clean Architecture Benefits:');
  console.log('✅ Single Responsibility - Each class has one job');
  console.log('✅ Dependency Injection - Easy to test and swap implementations');
  console.log('✅ Reusable Components - DocumentTool can be extracted as package');
  console.log('✅ Clean Interfaces - No implementation details leaked');
  console.log('✅ Easy Testing - Mock implementations for unit tests');
}

testCleanArchitecture().catch(console.error);
