{"name": "sarah-business-analyst", "version": "2.0.1", "description": "<PERSON> - AI Business Analyst with persistent memory and voice-first interaction", "type": "module", "main": "dist/index.js", "bin": {"sarah-business-analyst": "dist/index.js"}, "scripts": {"build": "tsc && chmod +x dist/index.js", "dev": "tsc --watch", "start": "node dist/index.js", "prepare": "npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "demo": "node demo/test-end-to-end.js"}, "keywords": ["ai-sdlc", "ai-teammate", "mcp-server", "persistent-memory", "voice-first", "intelligent-ai", "template"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@brainstack/integration-service": "workspace:*", "@modelcontextprotocol/sdk": "^0.5.0", "@octokit/rest": "^22.0.0", "dotenv": "^16.5.0"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20.0.0", "jest": "29.7.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}