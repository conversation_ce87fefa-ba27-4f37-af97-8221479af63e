{"name": "alex-architect", "version": "2.0.1", "description": "Alex - AI Architect with persistent memory and voice-first interaction", "type": "module", "main": "dist/index.js", "bin": {"alex-architect": "dist/index.js"}, "scripts": {"build": "tsc && chmod +x dist/index.js", "dev": "tsc --watch", "start": "node dist/index.js", "prepare": "npm run build"}, "keywords": ["ai-sdlc", "ai-teammate", "mcp-server", "persistent-memory", "voice-first", "intelligent-ai", "template"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}