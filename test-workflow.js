#!/usr/bin/env node

/**
 * Test script to verify <PERSON>'s new GitHub workflow tool
 */

import { spawn } from 'child_process';

async function testWorkflowTool() {
  console.log('🧪 Testing Sarah\'s GitHub workflow tool...');
  
  const sarah = spawn('node', ['apps/sarah-business-analyst/dist/index.js'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    cwd: '/root/AISDLC'
  });

  let output = '';
  
  sarah.stdout.on('data', (data) => {
    output += data.toString();
    console.log('📤 Sarah output:', data.toString());
  });

  sarah.stderr.on('data', (data) => {
    console.log('🔍 Sarah debug:', data.toString());
  });

  // Wait for <PERSON> to start
  await new Promise(resolve => setTimeout(resolve, 3000));

  // Test 1: List tools to see if workflow tool is available
  const listToolsRequest = {
    jsonrpc: "2.0",
    id: 1,
    method: "tools/list"
  };

  console.log('📝 Listing available tools...');
  sarah.stdin.write(JSON.stringify(listToolsRequest) + '\n');

  // Wait for response
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Test 2: Try to call the workflow tool
  const workflowRequest = {
    jsonrpc: "2.0",
    id: 2,
    method: "tools/call",
    params: {
      name: "complete-document-workflow",
      arguments: {
        issueNumber: 123,
        projectNumber: 1,
        documentUrl: "https://github.com/Infinisoft-inc/github-test/blob/main/test.md",
        documentTitle: "Test Document",
        reviewerMention: "@martin",
        taskDescription: "Testing workflow integration"
      }
    }
  };

  console.log('📝 Testing workflow tool...');
  sarah.stdin.write(JSON.stringify(workflowRequest) + '\n');

  // Wait for response
  await new Promise(resolve => setTimeout(resolve, 3000));

  sarah.kill();
  
  console.log('✅ Test completed. Check the output above for results.');
}

testWorkflowTool().catch(console.error);
