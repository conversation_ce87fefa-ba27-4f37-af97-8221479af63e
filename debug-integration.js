#!/usr/bin/env node

/**
 * Debug script to test integration service directly
 */

// Load environment variables manually
import { readFileSync } from 'fs';
try {
  const envContent = readFileSync('apps/sarah-business-analyst/.env', 'utf8');
  const lines = envContent.split('\n');
  for (const line of lines) {
    if (line.trim() && !line.startsWith('#')) {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    }
  }
} catch (error) {
  console.log('No .env file found, using existing environment variables');
}

async function debugIntegration() {
  console.log('🔍 Testing integration service directly...');
  
  const dopplerToken = process.env.DOPPLER_TOKEN;
  console.log('DOPPLER_TOKEN exists:', !!dopplerToken);
  console.log('DOPPLER_TOKEN length:', dopplerToken?.length || 0);
  
  if (!dopplerToken) {
    console.error('❌ No DOPPLER_TOKEN found');
    return;
  }

  try {
    console.log('\n1. Testing getGitHubCredentials...');
    const { getGitHubCredentials } = await import('@brainstack/integration-service');
    const githubResult = await getGitHubCredentials({ token: dopplerToken });
    console.log('GitHub credentials result:', JSON.stringify(githubResult, null, 2));

    if (githubResult.success) {
      console.log('\n2. Testing getInstallationId...');
      const { getInstallationId } = await import('@brainstack/integration-service');
      const installationResult = await getInstallationId(dopplerToken, 'Infinisoft-inc');
      console.log('Installation ID result:', JSON.stringify(installationResult, null, 2));

      console.log('\n3. Testing createGitHubSetup...');
      const { createGitHubSetup } = await import('@brainstack/integration-service');
      const setupResult = await createGitHubSetup(dopplerToken, 'Infinisoft-inc');
      console.log('GitHub setup result:', JSON.stringify(setupResult, null, 2));
    }
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

debugIntegration().catch(console.error);
